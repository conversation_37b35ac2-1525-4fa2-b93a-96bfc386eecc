@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', system-ui, sans-serif;
    font-weight: 600;
    line-height: 1.2;
  }
}

@layer components {
  .btn-primary {
    @apply inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 dark:border-gray-700;
  }

  .card-hover {
    @apply hover:shadow-2xl hover:shadow-primary-500/10 dark:hover:shadow-primary-400/10;
  }

  .glass {
    @apply bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  .section-padding {
    @apply py-20 lg:py-28;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  /* 3D Transform utilities */
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .transform-gpu {
    transform: translate3d(0, 0, 0);
  }

  .hover\:rotateY-5:hover {
    transform: rotateY(5deg);
  }

  .translateZ-4 {
    transform: translateZ(4px);
  }

  /* Line clamp utility */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Advanced 3D Effects */
  .card-3d {
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  }

  .card-3d:hover {
    transform: translateY(-20px) rotateX(15deg) rotateY(5deg) scale(1.05);
    box-shadow:
      0 40px 80px rgba(0, 0, 0, 0.2),
      0 0 40px rgba(59, 130, 246, 0.3),
      inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .card-face {
    backface-visibility: hidden;
    transform-style: preserve-3d;
  }

  .card-back {
    transform: rotateY(180deg);
  }

  /* Holographic effect */
  .holographic {
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    background-size: 200% 200%;
    animation: holographic 3s ease-in-out infinite;
  }

  @keyframes holographic {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Floating particles */
  .particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
    animation: float 6s ease-in-out infinite;
  }

  .particle:nth-child(1) { animation-delay: 0s; }
  .particle:nth-child(2) { animation-delay: 1s; }
  .particle:nth-child(3) { animation-delay: 2s; }
  .particle:nth-child(4) { animation-delay: 3s; }
  .particle:nth-child(5) { animation-delay: 4s; }

  /* Glass morphism enhanced */
  .glass-enhanced {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Neon glow effect */
  .neon-glow {
    box-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 40px currentColor;
  }

  /* 3D Text effect */
  .text-3d {
    text-shadow:
      1px 1px 0 #ccc,
      2px 2px 0 #c9c9c9,
      3px 3px 0 #bbb,
      4px 4px 0 #b9b9b9,
      5px 5px 0 #aaa,
      6px 6px 1px rgba(0,0,0,.1),
      0 0 5px rgba(0,0,0,.1),
      1px 1px 3px rgba(0,0,0,.3),
      3px 3px 5px rgba(0,0,0,.2),
      5px 5px 10px rgba(0,0,0,.25);
  }

  /* Perspective container */
  .perspective-container {
    perspective: 2000px;
    perspective-origin: center center;
  }

  /* Enhanced transform utilities */
  .transform-3d {
    transform-style: preserve-3d;
    will-change: transform;
  }

  .rotate-x-15 {
    transform: rotateX(15deg);
  }

  .rotate-y-15 {
    transform: rotateY(15deg);
  }

  .rotate-z-15 {
    transform: rotateZ(15deg);
  }
}

html[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

html[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Selection styling */
::selection {
  @apply bg-primary-500 text-white;
}

::-moz-selection {
  @apply bg-primary-500 text-white;
}
