{"hero": {"name": "بتقاوي محمد", "title": "مطوّر ويب", "description": "إنشاء تطبيقات سهلة الاستخدام وفعّالة باستخدام Laravel وReact js", "viewWork": "عرض مشاريعي", "contactMe": "اتصل بي"}, "about": {"title": "نبذة عني", "intro": "أنا مطوّر ويب شغوف أمتلك خبرة واسعة في بناء تطبيقات حديثة وقابلة للتطوير. متخصص في Laravel وReact js، أركّز على إنشاء تجارب مستخدم سلسة مدعومة بهياكل قوية من جهة الخادم.", "details": "بفضل قاعدة صلبة في تطوير الواجهات الأمامية والخلفية، أُحوّل الأفكار إلى واقع باستخدام كود نظيف وحلول مبتكرة. أتعلم باستمرار وأواكب التقنيات الحديثة لضمان أفضل النتائج الممكنة.", "frontend": "الواجهة الأمامية", "frontendSkills": "React js", "backend": "الواجهة الخلفية", "backendSkills": "Laravel 11, <PERSON><PERSON> 8,<PERSON> js, API RESTful"}, "contact": {"title": "تواصل معي", "contact_information": "معلومات الاتصال", "contact_intro": "أنا مهتم دائمًا بمشاريع وفرص جديدة. لا تتردد في التواصل معي عبر إحدى هذه القنوات:", "name": "الاسم", "email": "الب<PERSON>يد الإلكتروني", "message": "الرسالة", "send_message": "إرسال الرسالة", "sending": "جارٍ الإرسال...", "success_message": "تم إرسال الرسالة بنجاح!", "error_message": "فشل في إرسال الرسالة."}, "projects": {"title": "المشاريع المميزة", "demo": "تجربة", "code": "الكود", "project_1_title": "منصة للتجارة الإلكترونية", "project_1_description": "منصة تجارة إلكترونية متكاملة تم إنشاؤها باستخدام Laravel وReact، مع إدارة المخزون في الوقت الفعلي ومعالجة آمنة للمدفوعات.", "project_2_title": "منصة المزادات للمعدات الزراعية", "project_2_description": "AgriBid هي منصة المزادات للمعدات الزراعية، حيث يمكن للمهنيين في مجال الزراعة شراء وبيع المعدات عالية الجودة بأسعار تنافسية. من خلال واجهتنا البسيطة والآمنة، يمكنك المشاركة في المزادات بثقة والعثور على الأدوات التي تحتاجها لتحسين إنتاجك.", "project_3_title": "متجر ملابس للتجارة الإلكترونية", "project_3_description": "منصة تجارة إلكترونية متكاملة لبيع ملابس الرجال والنساء والأطفال. تم تطويرها باستخدام Express.js للواجهة الخلفية، React.js للواجهة الأمامية، MongoDB لقاعدة البيانات، وCloudinary لتخزين الصور. يتضمن المشروع واجهات منفصلة للمستخدمين والمسؤولين."}, "skills": {"title": "المهارات والخبرات", "category_1_title": "تطوير الواجهة الأمامية", "category_2_title": "تطوير الواجهة الخلفية", "category_3_title": "قواعد البيانات", "category_4_title": "لغات البرمجة", "skill_1_1": "React js", "skill_1_2": "Tailwind CSS", "skill_1_3": "Next.js", "skill_2_1": "Laravel 11", "skill_2_2": "PHP 8", "skill_2_3": "API RESTful", "skill_3_1": "MySQL", "skill_4_1": "PHP", "skill_4_2": "JavaScript", "skill_4_3": "Python", "skill_4_4": "Dart"}, "nav": {"home": "الرئيسية", "about": "من أنا", "projects": "المشاريع", "skills": "المهارات", "experience": "الخبرات", "contact": "اتصل بي"}}