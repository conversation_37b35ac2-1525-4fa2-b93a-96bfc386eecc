import React from 'react';
import {
  // Frontend Icons
  SiReact,
  SiJavascript,
  SiTypescript,
  SiHtml5,
  SiCss3,
  SiTailwindcss,
  SiBootstrap,
  SiSass,
  SiVuedotjs,
  SiAngular,
  SiNextdotjs,
  SiVite,
  SiWebpack,
  
  // Backend Icons
  SiNodedotjs,
  SiExpress,
  SiLaravel,
  SiPhp,
  SiPython,
  SiDjango,
  SiFlask,
  SiFastapi,
  SiSpring,
  SiJava,
  SiDotnet,
  SiCsharp,
  SiRubyonrails,
  SiRuby,
  
  // Database Icons
  SiMysql,
  SiPostgresql,
  SiMongodb,
  SiRedis,
  SiSqlite,
  SiFirebase,
  SiSupabase,
  SiPrisma,
  SiSequelize,
  
  // Tools & DevOps Icons
  SiGit,
  SiGithub,
  SiGitlab,
  SiBitbucket,
  SiDocker,
  SiKubernetes,
  SiJenkins,
  SiVercel,
  SiNetlify,
  SiHeroku,
  SiAmazonaws,
  SiGooglecloud,
  SiMicrosoftazure,
  SiLinux,
  SiUbuntu,
  SiWindows,
  SiMacos,
  SiVisualstudiocode,
  SiIntellijidea,
  SiPostman,
  SiInsomnia,
  SiFigma,
  SiAdobephotoshop,
  SiAdobeillustrator,
  SiSketch,
  SiSlack,
  SiDiscord,
  SiTrello,
  SiJira,
  SiNotion,
  
  // Mobile Icons
  SiReactnative,
  SiFlutter,
  SiDart,
  SiSwift,
  SiKotlin,
  SiAndroid,
  SiIos,
  
  // Testing Icons
  SiJest,
  SiCypress,
  SiSelenium,
  SiPytest,
  SiVitest,
  
  // Other Icons
  SiGraphql,
  SiApollographql,
  SiSocketdotio,
  SiElasticsearch,
  SiNginx,
  SiApache,
  SiYarn,
  SiNpm,
  SiPnpm,
  SiEslint,
  SiPrettier,
  SiBabel,
} from 'react-icons/si';

import {
  FaCode,
  FaServer,
  FaDatabase,
  FaTools,
  FaMobile,
  FaCloud,
  FaPalette,
  FaRocket,
  FaCog,
  FaLaptopCode,
} from 'react-icons/fa';

// Type pour les icônes
export interface SkillIcon {
  icon: React.ReactElement;
  color: string;
  bgColor: string;
}

// Mapping des compétences vers leurs icônes
export const skillIconMap: Record<string, SkillIcon> = {
  // Frontend Technologies
  'React': {
    icon: <SiReact />,
    color: '#61DAFB',
    bgColor: 'from-blue-400 to-cyan-400'
  },
  'JavaScript': {
    icon: <SiJavascript />,
    color: '#F7DF1E',
    bgColor: 'from-yellow-400 to-yellow-500'
  },
  'TypeScript': {
    icon: <SiTypescript />,
    color: '#3178C6',
    bgColor: 'from-blue-500 to-blue-600'
  },
  'HTML5': {
    icon: <SiHtml5 />,
    color: '#E34F26',
    bgColor: 'from-orange-500 to-red-500'
  },
  'CSS3': {
    icon: <SiCss3 />,
    color: '#1572B6',
    bgColor: 'from-blue-500 to-blue-600'
  },
  'Tailwind CSS': {
    icon: <SiTailwindcss />,
    color: '#06B6D4',
    bgColor: 'from-cyan-400 to-teal-500'
  },
  'Bootstrap': {
    icon: <SiBootstrap />,
    color: '#7952B3',
    bgColor: 'from-purple-500 to-indigo-600'
  },
  'Sass': {
    icon: <SiSass />,
    color: '#CC6699',
    bgColor: 'from-pink-400 to-purple-500'
  },
  'Vue.js': {
    icon: <SiVuedotjs />,
    color: '#4FC08D',
    bgColor: 'from-green-400 to-emerald-500'
  },
  'Angular': {
    icon: <SiAngular />,
    color: '#DD0031',
    bgColor: 'from-red-500 to-red-600'
  },
  'Next.js': {
    icon: <SiNextdotjs />,
    color: '#000000',
    bgColor: 'from-gray-800 to-black'
  },
  'Vite': {
    icon: <SiVite />,
    color: '#646CFF',
    bgColor: 'from-indigo-500 to-purple-600'
  },

  // Backend Technologies
  'Node.js': {
    icon: <SiNodedotjs />,
    color: '#339933',
    bgColor: 'from-green-500 to-green-600'
  },
  'Express.js': {
    icon: <SiExpress />,
    color: '#000000',
    bgColor: 'from-gray-700 to-gray-900'
  },
  'Laravel': {
    icon: <SiLaravel />,
    color: '#FF2D20',
    bgColor: 'from-red-500 to-red-600'
  },
  'PHP': {
    icon: <SiPhp />,
    color: '#777BB4',
    bgColor: 'from-indigo-500 to-purple-600'
  },
  'Python': {
    icon: <SiPython />,
    color: '#3776AB',
    bgColor: 'from-blue-500 to-yellow-400'
  },
  'Django': {
    icon: <SiDjango />,
    color: '#092E20',
    bgColor: 'from-green-800 to-green-900'
  },
  'Flask': {
    icon: <SiFlask />,
    color: '#000000',
    bgColor: 'from-gray-700 to-gray-900'
  },
  'FastAPI': {
    icon: <SiFastapi />,
    color: '#009688',
    bgColor: 'from-teal-500 to-green-600'
  },
  'Spring': {
    icon: <SiSpring />,
    color: '#6DB33F',
    bgColor: 'from-green-500 to-green-600'
  },
  'Java': {
    icon: <SiJava />,
    color: '#ED8B00',
    bgColor: 'from-orange-500 to-red-500'
  },

  // Databases
  'MySQL': {
    icon: <SiMysql />,
    color: '#4479A1',
    bgColor: 'from-blue-500 to-blue-600'
  },
  'PostgreSQL': {
    icon: <SiPostgresql />,
    color: '#336791',
    bgColor: 'from-blue-600 to-indigo-700'
  },
  'MongoDB': {
    icon: <SiMongodb />,
    color: '#47A248',
    bgColor: 'from-green-500 to-green-600'
  },
  'Redis': {
    icon: <SiRedis />,
    color: '#DC382D',
    bgColor: 'from-red-500 to-red-600'
  },
  'SQLite': {
    icon: <SiSqlite />,
    color: '#003B57',
    bgColor: 'from-blue-800 to-blue-900'
  },
  'Firebase': {
    icon: <SiFirebase />,
    color: '#FFCA28',
    bgColor: 'from-yellow-400 to-orange-500'
  },
  'Supabase': {
    icon: <SiSupabase />,
    color: '#3ECF8E',
    bgColor: 'from-green-400 to-emerald-500'
  },

  // Tools & DevOps
  'Git': {
    icon: <SiGit />,
    color: '#F05032',
    bgColor: 'from-orange-500 to-red-500'
  },
  'GitHub': {
    icon: <SiGithub />,
    color: '#181717',
    bgColor: 'from-gray-700 to-gray-900'
  },
  'Docker': {
    icon: <SiDocker />,
    color: '#2496ED',
    bgColor: 'from-blue-400 to-blue-600'
  },
  'Kubernetes': {
    icon: <SiKubernetes />,
    color: '#326CE5',
    bgColor: 'from-blue-500 to-indigo-600'
  },
  'AWS': {
    icon: <SiAmazonaws />,
    color: '#FF9900',
    bgColor: 'from-orange-400 to-yellow-500'
  },
  'Vercel': {
    icon: <SiVercel />,
    color: '#000000',
    bgColor: 'from-gray-800 to-black'
  },
  'Netlify': {
    icon: <SiNetlify />,
    color: '#00C7B7',
    bgColor: 'from-teal-400 to-cyan-500'
  },
  'Linux': {
    icon: <SiLinux />,
    color: '#FCC624',
    bgColor: 'from-yellow-400 to-orange-500'
  },
  'VS Code': {
    icon: <SiVisualstudiocode />,
    color: '#007ACC',
    bgColor: 'from-blue-500 to-blue-600'
  },
  'Postman': {
    icon: <SiPostman />,
    color: '#FF6C37',
    bgColor: 'from-orange-500 to-red-500'
  },
  'Figma': {
    icon: <SiFigma />,
    color: '#F24E1E',
    bgColor: 'from-purple-500 to-pink-500'
  },

  // Mobile Development
  'React Native': {
    icon: <SiReactnative />,
    color: '#61DAFB',
    bgColor: 'from-blue-400 to-cyan-400'
  },
  'Flutter': {
    icon: <SiFlutter />,
    color: '#02569B',
    bgColor: 'from-blue-500 to-cyan-500'
  },
  'Android': {
    icon: <SiAndroid />,
    color: '#3DDC84',
    bgColor: 'from-green-400 to-green-500'
  },
  'iOS': {
    icon: <SiIos />,
    color: '#000000',
    bgColor: 'from-gray-700 to-gray-900'
  },

  // Testing
  'Jest': {
    icon: <SiJest />,
    color: '#C21325',
    bgColor: 'from-red-500 to-red-600'
  },
  'Cypress': {
    icon: <SiCypress />,
    color: '#17202C',
    bgColor: 'from-gray-700 to-gray-900'
  },

  // Other Technologies
  'GraphQL': {
    icon: <SiGraphql />,
    color: '#E10098',
    bgColor: 'from-pink-500 to-purple-600'
  },
  'Socket.io': {
    icon: <SiSocketdotio />,
    color: '#010101',
    bgColor: 'from-gray-800 to-black'
  },
  'Nginx': {
    icon: <SiNginx />,
    color: '#009639',
    bgColor: 'from-green-500 to-green-600'
  },
  'Webpack': {
    icon: <SiWebpack />,
    color: '#8DD6F9',
    bgColor: 'from-blue-400 to-cyan-400'
  },

  // Default fallback icons
  'Frontend': {
    icon: <FaLaptopCode />,
    color: '#3B82F6',
    bgColor: 'from-blue-500 to-indigo-600'
  },
  'Backend': {
    icon: <FaServer />,
    color: '#10B981',
    bgColor: 'from-green-500 to-emerald-600'
  },
  'Database': {
    icon: <FaDatabase />,
    color: '#F59E0B',
    bgColor: 'from-yellow-500 to-orange-500'
  },
  'Tools': {
    icon: <FaTools />,
    color: '#8B5CF6',
    bgColor: 'from-purple-500 to-indigo-600'
  },
  'Mobile': {
    icon: <FaMobile />,
    color: '#EC4899',
    bgColor: 'from-pink-500 to-rose-500'
  },
  'Cloud': {
    icon: <FaCloud />,
    color: '#06B6D4',
    bgColor: 'from-cyan-400 to-blue-500'
  },
  'Design': {
    icon: <FaPalette />,
    color: '#F97316',
    bgColor: 'from-orange-500 to-red-500'
  },
  'DevOps': {
    icon: <FaRocket />,
    color: '#EF4444',
    bgColor: 'from-red-500 to-pink-500'
  },
  'Other': {
    icon: <FaCog />,
    color: '#6B7280',
    bgColor: 'from-gray-500 to-gray-600'
  },
  'Default': {
    icon: <FaCode />,
    color: '#6366F1',
    bgColor: 'from-indigo-500 to-purple-600'
  }
};

// Fonction pour obtenir l'icône d'une compétence
export const getSkillIcon = (skillName: string): SkillIcon => {
  // Recherche exacte
  if (skillIconMap[skillName]) {
    return skillIconMap[skillName];
  }
  
  // Recherche partielle (insensible à la casse)
  const normalizedSkill = skillName.toLowerCase();
  const foundKey = Object.keys(skillIconMap).find(key => 
    key.toLowerCase().includes(normalizedSkill) || 
    normalizedSkill.includes(key.toLowerCase())
  );
  
  if (foundKey) {
    return skillIconMap[foundKey];
  }
  
  // Fallback par catégorie
  if (normalizedSkill.includes('react') || normalizedSkill.includes('vue') || normalizedSkill.includes('angular')) {
    return skillIconMap['Frontend'];
  }
  if (normalizedSkill.includes('node') || normalizedSkill.includes('express') || normalizedSkill.includes('api')) {
    return skillIconMap['Backend'];
  }
  if (normalizedSkill.includes('sql') || normalizedSkill.includes('database') || normalizedSkill.includes('db')) {
    return skillIconMap['Database'];
  }
  if (normalizedSkill.includes('git') || normalizedSkill.includes('docker') || normalizedSkill.includes('deploy')) {
    return skillIconMap['Tools'];
  }
  
  // Icône par défaut
  return skillIconMap['Default'];
};
