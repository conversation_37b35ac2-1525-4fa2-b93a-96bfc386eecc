{"hero": {"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Web Developer", "description": "Crafting intuitive and high-performance applications with Laravel & React js", "viewWork": "View My Work", "contactMe": "Contact Me"}, "about": {"title": "About Me", "intro": "I'm a passionate Web Developer with extensive experience in building modern, scalable applications. Specializing in Laravel and React js, I focus on creating intuitive user experiences backed by robust server-side architecture.", "details": "With a strong foundation in both frontend and backend development, I bring ideas to life through clean code and innovative solutions. I'm constantly learning and adapting to new technologies to deliver the best possible results.", "frontend": "Frontend", "frontendSkills": "React js", "backend": "Backend", "backendSkills": "Laravel 11, <PERSON><PERSON> 8,Express js, RESTful APIs"}, "contact": {"title": "Get In Touch", "contact_information": "Contact Information", "contact_intro": "I'm always interested in hearing about new projects and opportunities. Feel free to reach out through any of these channels:", "name": "Name", "email": "Email", "message": "Message", "send_message": "Send Message", "sending": "Sending...", "success_message": "Message sent successfully!", "error_message": "Failed to send message."}, "projects": {"title": "Featured Projects", "demo": "Demo", "code": "Code", "project_1_title": "E-Commerce Platform", "project_1_description": "A full-featured e-commerce platform built with Laravel , featuring real-time inventory management and secure payment processing.", "project_2_title": "The Auction Platform for Agricultural Equipment", "project_2_description": "AgriBid is the auction platform for agricultural equipment, allowing professionals in the farming industry to buy and sell quality machinery at competitive prices. With our simple and secure interface, participate in auctions with confidence and find the tools you need to boost your production.", "project_3_title": "Clothing E-Commerce Store", "project_3_description": "A full-stack e-commerce platform for selling men's, women's, and kids' clothing. Built with Express.js for the backend, React.js for the frontend, MongoDB for the database, and Cloudinary for image storage. The project includes separate interfaces for users and administrators."}, "skills": {"title": "Skills & Expertise", "category_1_title": "Frontend Development", "category_2_title": "Backend Development", "category_3_title": "Database", "category_4_title": "Programming Languages", "skill_1_1": "React js", "skill_1_2": "Tailwind CSS", "skill_1_3": "Next.js", "skill_2_1": "Laravel 11", "skill_2_2": "PHP 8", "skill_2_3": "RESTful APIs", "skill_3_1": "MySQL", "skill_4_1": "PHP", "skill_4_2": "JavaScript", "skill_4_3": "Python", "skill_4_4": "Dart"}, "nav": {"home": "Home", "about": "About", "projects": "Projects", "skills": "Skills", "experience": "Experience", "contact": "Contact"}}