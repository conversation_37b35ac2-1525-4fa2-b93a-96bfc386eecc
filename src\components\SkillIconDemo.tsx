import React from 'react';
import { getSkillIcon } from '../data/skillIcons';

const SkillIconDemo = () => {
  const testSkills = [
    'React', 'JavaScript', 'TypeScript', 'HTML5', 'CSS3', 'Tailwind CSS',
    'Laravel', 'PHP', 'Node.js', 'Express.js', 'Python', 'Django',
    'MySQL', 'MongoDB', 'PostgreSQL', 'Firebase', 'Redis',
    'Git', 'GitHub', 'Docker', 'VS Code', 'Figma', 'Postman', 'AWS'
  ];

  return (
    <div className="p-8 bg-gray-100 dark:bg-gray-900 min-h-screen">
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
        Skill Icons Demo
      </h1>
      
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-6xl mx-auto">
        {testSkills.map((skill) => {
          const skillIcon = getSkillIcon(skill);
          return (
            <div
              key={skill}
              className="flex flex-col items-center p-4 bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              {/* Icon */}
              <div 
                className={`w-12 h-12 rounded-lg flex items-center justify-center mb-3 bg-gradient-to-r ${skillIcon.bgColor} shadow-lg`}
              >
                <div className="text-white text-xl">
                  {skillIcon.icon}
                </div>
              </div>
              
              {/* Skill Name */}
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 text-center">
                {skill}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SkillIconDemo;
