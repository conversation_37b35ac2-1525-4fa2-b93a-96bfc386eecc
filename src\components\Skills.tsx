import React, { useEffect } from 'react';
import { Code2, Database, Layout, Server } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getSkillIcon } from '../data/skillIcons';

const skillCategories = [
  {
    title: "Frontend Development",
    icon: <Layout className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["React", "JavaScript", "TypeScript", "HTML5", "CSS3", "Tailwind CSS", "Bootstrap"]
  },
  {
    title: "Backend Development",
    icon: <Server className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["Laravel", "PHP", "Node.js", "Express.js", "Python", "Django"]
  },
  {
    title: "Database & Storage",
    icon: <Database className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["MySQL", "MongoDB", "PostgreSQL", "Firebase", "Redis"]
  },
  {
    title: "Tools & DevOps",
    icon: <Code2 className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["Git", "GitHub", "Docker", "VS Code", "Figma", "Postman", "AWS"]
  }
];

const Skills = () => {
  const { t, i18n } = useTranslation();

  // Charger la langue préférée depuis localStorage au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en'; // Valeur par défaut : 'en'
    i18n.changeLanguage(savedLanguage);

    // Appliquer la direction RTL si la langue est arabe
    document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
  }, [i18n]);

  return (
    <section id="skills" className="section-padding bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-hero-pattern"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-display font-bold gradient-text mb-4">
            {t('skills.title')}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-600 to-accent-600 mx-auto rounded-full mb-6"></div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('skills.subtitle', { defaultValue: 'Technologies and tools I use to bring ideas to life.' })}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {skillCategories.map((category, index) => (
            <div key={index} className="card card-hover group p-8 text-center">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <div className="text-white">
                    {category.icon}
                  </div>
                </div>
              </div>

              <h3 className="text-xl font-display font-bold text-gray-900 dark:text-white mb-6 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
                {t(`skills.category_${index + 1}_title`, { defaultValue: category.title })}
              </h3>

              {/* Skills with Icons */}
              <div className="space-y-3">
                {category.skills.map((skill, skillIndex) => {
                  const skillIcon = getSkillIcon(skill);
                  return (
                    <div
                      key={skillIndex}
                      className="group relative flex items-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                    >
                      {/* Skill Icon */}
                      <div
                        className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center mr-4 bg-gradient-to-r ${skillIcon.bgColor} shadow-lg group-hover:scale-110 transition-transform duration-300`}
                      >
                        <div className="text-white text-lg">
                          {skillIcon.icon}
                        </div>
                      </div>

                      {/* Skill Name */}
                      <div className="flex-grow">
                        <span className="text-sm font-semibold text-gray-700 dark:text-gray-300 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
                          {t(`skills.skill_${index + 1}_${skillIndex + 1}`, { defaultValue: skill })}
                        </span>
                      </div>

                      {/* Skill Level Indicator */}
                      <div className="flex space-x-1 ml-4">
                        {[...Array(5)].map((_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              i < 4
                                ? 'bg-gradient-to-r from-primary-500 to-accent-500 shadow-sm group-hover:scale-125'
                                : 'bg-gray-300 dark:bg-gray-600 group-hover:bg-gray-400 dark:group-hover:bg-gray-500'
                            }`}
                          ></div>
                        ))}
                      </div>

                      {/* Hover Effect Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-accent-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">2+</div>
            <div className="text-gray-600 dark:text-gray-400">Years Experience</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent-600 dark:text-accent-400 mb-2">10+</div>
            <div className="text-gray-600 dark:text-gray-400">Projects Completed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">5+</div>
            <div className="text-gray-600 dark:text-gray-400">Technologies</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent-600 dark:text-accent-400 mb-2">100%</div>
            <div className="text-gray-600 dark:text-gray-400">Client Satisfaction</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
