import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate environment variables
if (!supabaseUrl || !supabaseKey) {
  console.warn('Supabase configuration missing. Some features may not work.');
}

// Create Supabase client with error handling
let supabase: SupabaseClient | null = null;

try {
  if (supabaseUrl && supabaseKey) {
    supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
      },
    });
  }
} catch (error) {
  console.error('Failed to initialize Supabase client:', error);
}

// Mock client for development when Supabase is not available
const mockSupabase = {
  auth: {
    signInWithPassword: async () => ({ error: { message: 'Supabase not configured' } }),
    signOut: async () => ({ error: null }),
  },
  from: () => ({
    select: () => ({
      order: () => ({ data: [], error: null }),
    }),
    insert: () => ({ data: null, error: null }),
    delete: () => ({ data: null, error: null }),
  }),
  channel: () => ({
    on: () => ({
      on: () => ({
        subscribe: () => {},
      }),
    }),
  }),
};

export default supabase || mockSupabase;
