import { useEffect, useState, useRef } from 'react';
import { ExternalLink, Github, ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const projects = [
  {
    title: "E-Commerce Platform",
    description: "A full-featured e-commerce platform built with Laravel , featuring real-time inventory management and secure payment processing.",
    image: "https://images.unsplash.com/photo-1557821552-17105176677c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    technologies: ["Laravel 11", "MySQL"],
    githubLink: "https://github.com/MohammedBetkaoui/store_laravel_11.git",
    liveLink: undefined
  },
  {
    title: "La plateforme d'enchères pour le matériel agricole",
    description: "AgriBid is the auction platform for agricultural equipment, allowing professionals in the farming industry to buy and sell quality machinery at competitive prices. With our simple and secure interface, participate in auctions with confidence and find the tools you need to boost your production.",
    image: "/images/pexels-pixabay-163752.jpg",
    technologies: ["Laravel", "MySQL", "WebSockets"],
    githubLink: "https://github.com/MohammedBetkaoui/agriculture.git",
    liveLink: undefined
  },
  {
    title: "E-Commerce Clothing Store",
    description: "A full-stack e-commerce platform for selling men's, women's, and kids' clothing. Built with Express.js for the backend, React.js for the frontend, MongoDB for the database, and Cloudinary for image storage. The project includes separate interfaces for users and administrators.",
    image: "https://images.unsplash.com/photo-1523381210434-271e8be1f52b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    technologies: ["Express.js", "React.js", "MongoDB", "Cloudinary"],
    githubLink: "https://github.com/MohammedBetkaoui/btk-shop.git",
    liveLink: undefined
  }
];
const Projects = () => {
  const { t, i18n } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Charger la langue préférée au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    i18n.changeLanguage(savedLanguage);
    document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
  }, [i18n]);

  // Auto-play carousel
  useEffect(() => {
    if (isAutoPlay && !isHovered) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % projects.length);
      }, 4000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAutoPlay, isHovered]);

  // Navigation functions
  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % projects.length);
  };

  const goToPrev = () => {
    setCurrentIndex((prev) => (prev - 1 + projects.length) % projects.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  return (
    <section id="projects" className="section-padding bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-0 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-accent-500/10 rounded-full blur-3xl animate-pulse-slow animation-delay-400"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-primary-500/5 to-accent-500/5 rounded-full blur-2xl animate-bounce-slow"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-6xl font-display font-bold gradient-text mb-6 animate-slide-up">
            {t('projects.title')}
          </h2>
          <div className="w-32 h-1.5 bg-gradient-to-r from-primary-600 via-accent-500 to-primary-600 mx-auto rounded-full mb-8 animate-gradient"></div>
          <p className="text-xl lg:text-2xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed animate-slide-up animation-delay-200">
            {t('projects.subtitle', { defaultValue: 'Découvrez mes projets les plus récents avec des animations 3D interactives.' })}
          </p>
        </div>

        {/* Enhanced 3D Carousel Container */}
        <div
          className="relative perspective-container h-[700px] lg:h-[800px] overflow-hidden"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Floating Particles */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className={`particle w-2 h-2 bg-primary-400/30 absolute animate-float`}
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${i * 0.5}s`,
                  animationDuration: `${4 + Math.random() * 4}s`,
                }}
              />
            ))}
          </div>

          {/* Holographic Background */}
          <div className="absolute inset-0 holographic opacity-20"></div>
          {/* Projects Carousel */}
          <div className="relative w-full h-full flex items-center justify-center">
            {projects.map((project, index) => {
              const offset = index - currentIndex;
              const absOffset = Math.abs(offset);
              const isActive = index === currentIndex;

              return (
                <div
                  key={index}
                  className={`absolute transition-all duration-700 ease-in-out transform-gpu ${
                    isActive ? 'z-30' : absOffset === 1 ? 'z-20' : 'z-10'
                  }`}
                  style={{
                    transform: `
                      translateX(${offset * 300}px)
                      translateZ(${isActive ? 0 : -absOffset * 200}px)
                      rotateY(${offset * 25}deg)
                      scale(${isActive ? 1 : 1 - absOffset * 0.2})
                    `,
                    opacity: absOffset > 2 ? 0 : 1 - absOffset * 0.3,
                  }}
                >
                  {/* Enhanced 3D Project Card */}
                  <div className={`
                    relative w-80 lg:w-96 h-96 lg:h-[520px]
                    transform-3d transition-all duration-700 ease-out
                    ${isActive ? 'card-3d animate-glow-pulse' : ''}
                    cursor-pointer group
                  `}
                  onClick={() => goToSlide(index)}
                  >
                    {/* Enhanced Card Shadow with multiple layers */}
                    <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-black/20 to-black/10 rounded-2xl transform translateZ-4 blur-2xl"></div>
                    <div className="absolute inset-0 bg-primary-500/20 rounded-2xl transform translateZ-8 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Holographic overlay */}
                    <div className={`absolute inset-0 rounded-2xl holographic transition-opacity duration-500 ${
                      isActive ? 'opacity-30' : 'opacity-0'
                    }`}></div>

                    {/* Main Card with glass effect */}
                    <div className={`
                      relative w-full h-full rounded-2xl overflow-hidden
                      transform-gpu transition-all duration-700
                      ${isActive
                        ? 'bg-white/95 dark:bg-gray-800/95 shadow-glow-lg glass-enhanced neon-glow text-primary-500'
                        : 'bg-white/90 dark:bg-gray-800/90 shadow-2xl'
                      }
                      border border-white/20 dark:border-gray-700/20
                      backdrop-blur-xl
                    `}>
                      {/* Enhanced Image Section with 3D effects */}
                      <div className="relative h-52 lg:h-60 overflow-hidden">
                        {/* Image with parallax effect */}
                        <div className="relative w-full h-full transform-3d">
                          <img
                            src={project.image}
                            alt={project.title}
                            className={`w-full h-full object-cover transition-all duration-1000 transform-gpu ${
                              isActive
                                ? 'scale-115 rotate-1 brightness-110 contrast-110 saturate-110'
                                : 'scale-100 brightness-90'
                            }`}
                          />

                          {/* Animated gradient overlays */}
                          <div className={`absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent transition-all duration-700 ${
                            isActive ? 'opacity-100' : 'opacity-40'
                          }`}></div>

                          <div className={`absolute inset-0 bg-gradient-to-br from-primary-500/20 via-transparent to-accent-500/20 transition-all duration-700 ${
                            isActive ? 'opacity-100' : 'opacity-0'
                          }`}></div>

                          {/* Holographic scan line */}
                          <div className={`absolute inset-0 bg-gradient-to-b from-transparent via-white/20 to-transparent h-8 transition-all duration-2000 ${
                            isActive ? 'animate-pulse translate-y-full' : 'opacity-0'
                          }`}></div>
                        </div>

                        {/* Enhanced Floating Action Buttons */}
                        <div className={`absolute top-4 right-4 flex flex-col space-y-2 transition-all duration-700 transform ${
                          isActive ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 -translate-y-8 scale-75'
                        }`}>
                          <a
                            href={project.githubLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group p-3 glass-enhanced hover:bg-white/20 rounded-xl shadow-2xl hover:shadow-glow transition-all duration-500 transform hover:scale-125 hover:rotate-12 hover:-translate-y-2"
                          >
                            <Github size={20} className="text-white group-hover:text-primary-400 transition-colors duration-300" />
                          </a>
                          {project.liveLink && (
                            <a
                              href={project.liveLink}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="group p-3 glass-enhanced hover:bg-white/20 rounded-xl shadow-2xl hover:shadow-glow transition-all duration-500 transform hover:scale-125 hover:rotate-12 hover:-translate-y-2"
                            >
                              <ExternalLink size={20} className="text-white group-hover:text-accent-400 transition-colors duration-300" />
                            </a>
                          )}
                        </div>

                        {/* Project number indicator */}
                        <div className={`absolute top-4 left-4 transition-all duration-700 ${
                          isActive ? 'opacity-100 scale-100' : 'opacity-0 scale-75'
                        }`}>
                          <div className="w-12 h-12 glass-enhanced rounded-full flex items-center justify-center text-white font-bold text-lg shadow-glow">
                            {index + 1}
                          </div>
                        </div>
                      </div>

                      {/* Enhanced Content Section */}
                      <div className="p-6 lg:p-8 h-48 lg:h-60 flex flex-col justify-between relative">
                        {/* Animated background pattern */}
                        <div className={`absolute inset-0 opacity-5 transition-opacity duration-700 ${
                          isActive ? 'opacity-10' : 'opacity-0'
                        }`}>
                          <div className="w-full h-full bg-gradient-to-br from-primary-500/10 to-accent-500/10 animate-pulse"></div>
                        </div>

                        <div className="relative z-10">
                          <h3 className={`text-xl lg:text-2xl font-display font-bold mb-3 transition-all duration-700 transform ${
                            isActive
                              ? 'text-primary-600 dark:text-primary-400 scale-105 text-3d translate-y-0'
                              : 'text-gray-900 dark:text-white scale-100 translate-y-2'
                          }`}>
                            {t(`projects.project_${index + 1}_title`, { defaultValue: project.title })}
                          </h3>

                          <p className={`text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed transition-all duration-700 ${
                            isActive ? 'opacity-100 translate-y-0' : 'opacity-80 translate-y-1'
                          }`}>
                            {t(`projects.project_${index + 1}_description`, { defaultValue: project.description })}
                          </p>
                        </div>

                        {/* Enhanced Technologies with 3D effects */}
                        <div className="flex flex-wrap gap-2 relative z-10">
                          {project.technologies.map((tech, techIndex) => (
                            <span
                              key={techIndex}
                              className={`px-3 py-1.5 text-xs font-medium rounded-full transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 ${
                                isActive
                                  ? 'bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/40 dark:to-accent-900/40 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-600 shadow-lg hover:shadow-glow'
                                  : 'bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                              }`}
                              style={{
                                animationDelay: `${techIndex * 100}ms`,
                                transform: isActive ? `translateY(0) scale(1)` : `translateY(10px) scale(0.95)`,
                              }}
                            >
                              {tech}
                            </span>
                          ))}
                        </div>

                        {/* Progress bar for active card */}
                        {isActive && (
                          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-accent-500 animate-pulse"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Enhanced Navigation Controls with 3D effects */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-8 z-40">
            {/* Previous Button with 3D effect */}
            <button
              onClick={goToPrev}
              className="group p-4 glass-enhanced hover:bg-white/20 rounded-2xl shadow-2xl hover:shadow-glow transition-all duration-500 transform hover:scale-125 hover:-translate-y-2 hover:rotate-12 active:scale-95"
            >
              <ChevronLeft size={28} className="text-white group-hover:text-primary-400 transition-all duration-300 group-hover:scale-110" />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary-500/20 to-accent-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>

            {/* Enhanced Dots Indicator with 3D effects */}
            <div className="flex space-x-3 p-3 glass-enhanced rounded-2xl shadow-xl">
              {projects.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`relative transition-all duration-500 transform hover:scale-150 hover:-translate-y-1 ${
                    index === currentIndex
                      ? 'w-4 h-4 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full shadow-glow scale-125 animate-glow-pulse'
                      : 'w-3 h-3 bg-white/60 hover:bg-white/90 rounded-full hover:shadow-lg'
                  }`}
                >
                  {index === currentIndex && (
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary-400 to-accent-400 animate-ping"></div>
                  )}
                </button>
              ))}
            </div>

            {/* Next Button with 3D effect */}
            <button
              onClick={goToNext}
              className="group p-4 glass-enhanced hover:bg-white/20 rounded-2xl shadow-2xl hover:shadow-glow transition-all duration-500 transform hover:scale-125 hover:-translate-y-2 hover:-rotate-12 active:scale-95"
            >
              <ChevronRight size={28} className="text-white group-hover:text-primary-400 transition-all duration-300 group-hover:scale-110" />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary-500/20 to-accent-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          </div>

          {/* Enhanced Auto-play Control */}
          <div className="absolute top-8 right-8 z-40">
            <button
              onClick={toggleAutoPlay}
              className={`group p-4 glass-enhanced rounded-2xl shadow-2xl transition-all duration-500 transform hover:scale-125 hover:-translate-y-2 active:scale-95 ${
                isAutoPlay
                  ? 'hover:bg-red-500/20 hover:shadow-red-500/30'
                  : 'hover:bg-green-500/20 hover:shadow-green-500/30'
              }`}
              title={isAutoPlay ? 'Pause auto-play' : 'Start auto-play'}
            >
              {isAutoPlay ? (
                <Pause size={24} className="text-white group-hover:text-red-400 transition-all duration-300 group-hover:scale-110" />
              ) : (
                <Play size={24} className="text-white group-hover:text-green-400 transition-all duration-300 group-hover:scale-110" />
              )}
              <div className={`absolute inset-0 rounded-2xl transition-opacity duration-300 ${
                isAutoPlay
                  ? 'bg-gradient-to-r from-red-500/20 to-red-600/20 opacity-0 group-hover:opacity-100'
                  : 'bg-gradient-to-r from-green-500/20 to-green-600/20 opacity-0 group-hover:opacity-100'
              }`}></div>
            </button>
          </div>
        </div>

        {/* Enhanced Project Counter with 3D effects */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-6 glass-enhanced rounded-2xl px-8 py-4 shadow-2xl hover:shadow-glow transition-all duration-500 transform hover:scale-105 hover:-translate-y-1">
            {/* Counter with animated background */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-accent-500/20 rounded-lg animate-pulse"></div>
              <span className="relative text-lg font-bold text-white px-3 py-1">
                {currentIndex + 1} / {projects.length}
              </span>
            </div>

            {/* Animated separator */}
            <div className="w-px h-6 bg-gradient-to-b from-transparent via-white/60 to-transparent animate-pulse"></div>

            {/* Project title with 3D text effect */}
            <span className="text-lg font-bold text-white text-3d max-w-xs truncate">
              {t(`projects.project_${currentIndex + 1}_title`, { defaultValue: projects[currentIndex].title })}
            </span>

            {/* Progress indicator */}
            <div className="flex space-x-1">
              {projects.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-500 ${
                    index <= currentIndex
                      ? 'bg-gradient-to-r from-primary-400 to-accent-400 shadow-glow'
                      : 'bg-white/30'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* View All Projects Button */}
        <div className="text-center mt-8">
          <a
            href="https://github.com/MohammedBetkaoui"
            className="btn-secondary group"
            target="_blank"
            rel="noopener noreferrer"
          >
            <span>Voir Tous les Projets</span>
            <ExternalLink className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Projects;
