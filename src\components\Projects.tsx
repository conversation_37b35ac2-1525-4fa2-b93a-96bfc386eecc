import { useEffect, useState, useRef } from 'react';
import { ExternalLink, Github, ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const projects = [
  {
    title: "E-Commerce Platform",
    description: "A full-featured e-commerce platform built with Laravel , featuring real-time inventory management and secure payment processing.",
    image: "https://images.unsplash.com/photo-1557821552-17105176677c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    technologies: ["Laravel 11", "MySQL"],
    githubLink: "https://github.com/MohammedBetkaoui/store_laravel_11.git",
    liveLink: undefined
  },
  {
    title: "La plateforme d'enchères pour le matériel agricole",
    description: "AgriBid is the auction platform for agricultural equipment, allowing professionals in the farming industry to buy and sell quality machinery at competitive prices. With our simple and secure interface, participate in auctions with confidence and find the tools you need to boost your production.",
    image: "/images/pexels-pixabay-163752.jpg",
    technologies: ["Laravel", "MySQL", "WebSockets"],
    githubLink: "https://github.com/MohammedBetkaoui/agriculture.git",
    liveLink: undefined
  },
  {
    title: "E-Commerce Clothing Store",
    description: "A full-stack e-commerce platform for selling men's, women's, and kids' clothing. Built with Express.js for the backend, React.js for the frontend, MongoDB for the database, and Cloudinary for image storage. The project includes separate interfaces for users and administrators.",
    image: "https://images.unsplash.com/photo-1523381210434-271e8be1f52b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    technologies: ["Express.js", "React.js", "MongoDB", "Cloudinary"],
    githubLink: "https://github.com/MohammedBetkaoui/btk-shop.git",
    liveLink: undefined
  }
];
const Projects = () => {
  const { t, i18n } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Charger la langue préférée au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    i18n.changeLanguage(savedLanguage);
    document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
  }, [i18n]);

  // Auto-play carousel
  useEffect(() => {
    if (isAutoPlay && !isHovered) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % projects.length);
      }, 4000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAutoPlay, isHovered]);

  // Navigation functions
  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % projects.length);
  };

  const goToPrev = () => {
    setCurrentIndex((prev) => (prev - 1 + projects.length) % projects.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  return (
    <section id="projects" className="section-padding bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-0 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-accent-500/10 rounded-full blur-3xl animate-pulse-slow animation-delay-400"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-primary-500/5 to-accent-500/5 rounded-full blur-2xl animate-bounce-slow"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-6xl font-display font-bold gradient-text mb-6 animate-slide-up">
            {t('projects.title')}
          </h2>
          <div className="w-32 h-1.5 bg-gradient-to-r from-primary-600 via-accent-500 to-primary-600 mx-auto rounded-full mb-8 animate-gradient"></div>
          <p className="text-xl lg:text-2xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed animate-slide-up animation-delay-200">
            {t('projects.subtitle', { defaultValue: 'Découvrez mes projets les plus récents avec des animations 3D interactives.' })}
          </p>
        </div>

        {/* 3D Carousel Container */}
        <div
          className="relative perspective-1000 h-[600px] lg:h-[700px]"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Projects Carousel */}
          <div className="relative w-full h-full flex items-center justify-center">
            {projects.map((project, index) => {
              const offset = index - currentIndex;
              const absOffset = Math.abs(offset);
              const isActive = index === currentIndex;

              return (
                <div
                  key={index}
                  className={`absolute transition-all duration-700 ease-in-out transform-gpu ${
                    isActive ? 'z-30' : absOffset === 1 ? 'z-20' : 'z-10'
                  }`}
                  style={{
                    transform: `
                      translateX(${offset * 300}px)
                      translateZ(${isActive ? 0 : -absOffset * 200}px)
                      rotateY(${offset * 25}deg)
                      scale(${isActive ? 1 : 1 - absOffset * 0.2})
                    `,
                    opacity: absOffset > 2 ? 0 : 1 - absOffset * 0.3,
                  }}
                >
                  {/* 3D Project Card */}
                  <div className={`
                    relative w-80 lg:w-96 h-96 lg:h-[500px]
                    transform-gpu transition-all duration-500
                    ${isActive ? 'hover:scale-105 hover:rotateY-5' : ''}
                    preserve-3d
                  `}>
                    {/* Card Shadow */}
                    <div className="absolute inset-0 bg-black/20 rounded-2xl transform translateZ-4 blur-xl"></div>

                    {/* Main Card */}
                    <div className={`
                      relative w-full h-full bg-white dark:bg-gray-800 rounded-2xl overflow-hidden
                      shadow-2xl border border-gray-200 dark:border-gray-700
                      transform-gpu transition-all duration-500
                      ${isActive ? 'shadow-glow-lg' : 'shadow-xl'}
                    `}>
                      {/* Image Section */}
                      <div className="relative h-48 lg:h-56 overflow-hidden">
                        <img
                          src={project.image}
                          alt={project.title}
                          className={`w-full h-full object-cover transition-transform duration-700 ${
                            isActive ? 'scale-110' : 'scale-100'
                          }`}
                        />
                        <div className={`absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent transition-opacity duration-500 ${
                          isActive ? 'opacity-100' : 'opacity-0'
                        }`}></div>

                        {/* Floating Action Buttons */}
                        <div className={`absolute top-4 right-4 flex space-x-2 transition-all duration-500 ${
                          isActive ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'
                        }`}>
                          <a
                            href={project.githubLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-3 bg-white/90 hover:bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 hover:rotate-12"
                          >
                            <Github size={20} className="text-gray-800" />
                          </a>
                          {project.liveLink && (
                            <a
                              href={project.liveLink}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="p-3 bg-white/90 hover:bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 hover:rotate-12"
                            >
                              <ExternalLink size={20} className="text-gray-800" />
                            </a>
                          )}
                        </div>
                      </div>

                      {/* Content Section */}
                      <div className="p-6 lg:p-8 h-48 lg:h-64 flex flex-col justify-between">
                        <div>
                          <h3 className={`text-xl lg:text-2xl font-display font-bold text-gray-900 dark:text-white mb-3 transition-all duration-500 ${
                            isActive ? 'text-primary-600 dark:text-primary-400' : ''
                          }`}>
                            {t(`projects.project_${index + 1}_title`, { defaultValue: project.title })}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed">
                            {t(`projects.project_${index + 1}_description`, { defaultValue: project.description })}
                          </p>
                        </div>

                        {/* Technologies */}
                        <div className="flex flex-wrap gap-2">
                          {project.technologies.map((tech, techIndex) => (
                            <span
                              key={techIndex}
                              className={`px-3 py-1 text-xs font-medium rounded-full transition-all duration-300 ${
                                isActive
                                  ? 'bg-gradient-to-r from-primary-100 to-accent-100 dark:from-primary-900/30 dark:to-accent-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-700'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                              }`}
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Navigation Controls */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex items-center space-x-6 z-40">
            {/* Previous Button */}
            <button
              onClick={goToPrev}
              className="p-3 bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group"
            >
              <ChevronLeft size={24} className="text-gray-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400" />
            </button>

            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {projects.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToSlide(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-primary-600 scale-125 shadow-lg'
                      : 'bg-white/60 hover:bg-white/80'
                  }`}
                />
              ))}
            </div>

            {/* Next Button */}
            <button
              onClick={goToNext}
              className="p-3 bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group"
            >
              <ChevronRight size={24} className="text-gray-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400" />
            </button>
          </div>

          {/* Auto-play Control */}
          <div className="absolute top-8 right-8 z-40">
            <button
              onClick={toggleAutoPlay}
              className="p-3 bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-800 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group"
              title={isAutoPlay ? 'Pause auto-play' : 'Start auto-play'}
            >
              {isAutoPlay ? (
                <Pause size={20} className="text-gray-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400" />
              ) : (
                <Play size={20} className="text-gray-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400" />
              )}
            </button>
          </div>
        </div>

        {/* Project Counter */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center space-x-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
              {currentIndex + 1} / {projects.length}
            </span>
            <div className="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
            <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
              {t(`projects.project_${currentIndex + 1}_title`, { defaultValue: projects[currentIndex].title })}
            </span>
          </div>
        </div>

        {/* View All Projects Button */}
        <div className="text-center mt-8">
          <a
            href="https://github.com/MohammedBetkaoui"
            className="btn-secondary group"
            target="_blank"
            rel="noopener noreferrer"
          >
            <span>Voir Tous les Projets</span>
            <ExternalLink className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Projects;
