{"hero": {"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Développeur Web", "description": "Création d'applications intuitives et performantes avec Laravel & React js", "viewWork": "Voir mes projets", "contactMe": "<PERSON>ez-moi"}, "about": {"title": "À propos de moi", "intro": "Je suis un développeur web passionné avec une expérience approfondie dans la création d'applications modernes et évolutives. Spécialisé dans Laravel et React js, je me concentre sur la création d'expériences utilisateur intuitives soutenues par une architecture robuste côté serveur.", "details": "Avec une solide base en développement frontend et backend, je donne vie aux idées grâce à un code propre et des solutions innovantes. Je suis constamment en train d'apprendre et de m'adapter aux nouvelles technologies pour offrir les meilleurs résultats possibles.", "frontend": "Frontend", "frontendSkills": "React js", "backend": "Backend ", "backendSkills": "Laravel 11, <PERSON><PERSON> 8, <PERSON> js, API RESTful"}, "contact": {"title": "Prenez Contact", "contact_information": "Informations de Contact", "contact_intro": "Je suis toujours intéressé par de nouveaux projets et opportunités. N'hésitez pas à me contacter via l'un de ces canaux :", "name": "Nom", "email": "Email", "message": "Message", "send_message": "Envoyer le <PERSON>", "sending": "Envoi...", "success_message": "Message envoyé avec succès !", "error_message": "Échec de l'envoi du message."}, "projects": {"title": "Projets en vedette", "demo": "Démo", "code": "Code", "project_1_title": "Plateforme de E-Commerce", "project_1_description": "Une plateforme e-commerce complète construite avec <PERSON> , avec gestion des stocks en temps réel et traitement sécurisé des paiements.", "project_2_title": "La plateforme d'enchères pour le matériel agricole", "project_2_description": "AgriBid est la plateforme de vente aux enchères dédiée au matériel agricole, permettant aux professionnels de l'agriculture d'acheter et de vendre des équipements de qualité à des prix compétitifs. Grâce à notre interface simple et sécurisée, participez aux enchères en toute confiance et trouvez les outils qu'il vous faut pour améliorer votre production.", "project_3_title": "Boutique en ligne de vêtements", "project_3_description": "Une plateforme e-commerce complète pour la vente de vêtements pour hommes, femmes et enfants. Développée avec Express.js pour le backend, React.js pour le frontend, MongoDB pour la base de données, et Cloudinary pour le stockage des images. Le projet comprend des interfaces séparées pour les utilisateurs et les administrateurs."}, "skills": {"title": "Compétences et Expertise", "category_1_title": "Développement Frontend", "category_2_title": "Développement Backend", "category_3_title": "Base de données", "category_4_title": "Langages de programmation", "skill_1_1": "React js", "skill_1_2": "Tailwind CSS", "skill_1_3": "Next.js", "skill_2_1": "Laravel 11", "skill_2_2": "PHP 8", "skill_2_3": "API RESTful", "skill_3_1": "MySQL", "skill_4_1": "PHP", "skill_4_2": "JavaScript", "skill_4_3": "Python", "skill_4_4": "Dart"}, "nav": {"home": "Accueil", "about": "À propos", "projects": "Projets", "skills": "Compétences", "experience": "Expérience", "contact": "Contact"}}